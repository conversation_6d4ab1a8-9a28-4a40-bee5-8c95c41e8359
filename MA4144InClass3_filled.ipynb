#%% md

# <center>Recurrent Neural Networks</center>
## <center>In‑class Project 3 – MA4144</center>

**Goal:** Build a small RNN that autocompletes half‑typed words using a supplied word list.

**Runtime target:** ~1–1.5 minutes on CPU (kept small via tiny model + few epochs).

---

#%%

import sys, os, time, random, math, json
from pathlib import Path

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader

print("Python:", sys.version)
print("PyTorch:", torch.__version__)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Device:", device)

#%%

WORDLIST_PATH = Path('wordlist.txt')
assert WORDLIST_PATH.exists(), "Missing /mnt/data/wordlist.txt"

# Load words (lowercase, ascii letters only) and keep moderate length words
raw_words = [w.strip() for w in WORDLIST_PATH.read_text(encoding='utf-8', errors='ignore').splitlines()]
words = []
for w in raw_words:
    wl = w.strip().lower()
    if len(wl) >= 3 and wl.isalpha():  # keep only alphabetic words (no digits/symbols)
        words.append(wl)

print(f"Loaded words: {len(raw_words):,} | Filtered usable: {len(words):,}")
# Deduplicate while preserving order
seen = set()
unique_words = []
for w in words:
    if w not in seen:
        unique_words.append(w); seen.add(w)

# Subsample to keep training fast (tune N for your machine)
MAX_WORDS = 35000  # adjust if you need even faster
unique_words = unique_words[:MAX_WORDS]
print(f"Unique usable words: {len(unique_words):,} (cap={MAX_WORDS})")
unique_words[:15]

#%%

# Character vocabulary (a-z plus start/end tokens)
alphabet = list("abcdefghijklmnopqrstuvwxyz")
PAD = "<pad>"
SOS = "<s>"   # start of sequence
EOS = "</s>"  # end of sequence

itos = [PAD, SOS, EOS] + alphabet
stoi = {ch:i for i,ch in enumerate(itos)}

vocab_size = len(itos)
print("Vocab size:", vocab_size, "| tokens:", itos)

def encode(text: str):
    # sequence: <s> + chars + </s>
    ids = [stoi[SOS]] + [stoi[c] for c in text] + [stoi[EOS]]
    return ids

def decode(ids):
    chars = []
    for i in ids:
        if i == stoi[EOS]: break
        if i in (stoi[SOS], stoi[PAD]): 
            continue
        chars.append(itos[i])
    return "".join(chars)

#%%

class NextCharDataset(Dataset):
    def __init__(self, words, max_len=32):
        seqs = []
        for w in words:
            if len(w) + 2 > max_len:  # +2 for SOS/EOS
                continue
            ids = encode(w)
            # create (input, target) pairs for next-char prediction
            # input includes up to last char before EOS; target is next char
            for t in range(1, len(ids)):  # start predicting after SOS
                x = ids[:t]                 # [SOS, c1, c2, ...]
                y = ids[t]                  # next token
                seqs.append((x, y))
        self.samples = seqs
        self.max_len = max_len

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        x, y = self.samples[idx]
        # pad to max_len
        x_pad = x + [stoi[PAD]]*(self.max_len - len(x))
        return torch.tensor(x_pad, dtype=torch.long), torch.tensor(y, dtype=torch.long)

dataset_full = NextCharDataset(unique_words, max_len=32)
print("Samples:", len(dataset_full))

# Train/val split by word indices (keep pairs associated with those words)
# Simpler: split words, then re-create datasets for each split so no leakage.
split = int(0.9 * len(unique_words))
train_words = unique_words[:split]
val_words   = unique_words[split:]

train_ds = NextCharDataset(train_words, max_len=32)
val_ds   = NextCharDataset(val_words, max_len=32)
print("Train samples:", len(train_ds), "| Val samples:", len(val_ds))

#%%

BATCH_SIZE = 256

def collate(batch):
    xs = torch.stack([b[0] for b in batch], dim=0)
    ys = torch.stack([b[1] for b in batch], dim=0)
    return xs, ys

train_loader = DataLoader(train_ds, batch_size=BATCH_SIZE, shuffle=True, num_workers=0, collate_fn=collate, drop_last=False)
val_loader   = DataLoader(val_ds,   batch_size=BATCH_SIZE, shuffle=False, num_workers=0, collate_fn=collate, drop_last=False)

len(train_loader), len(val_loader)

#%%

class CharGRU(nn.Module):
    def __init__(self, vocab_size, emb_dim=64, hidden=128, num_layers=1):
        super().__init__()
        self.emb = nn.Embedding(vocab_size, emb_dim, padding_idx=stoi["<pad>"])
        self.gru = nn.GRU(emb_dim, hidden, num_layers=num_layers, batch_first=True)
        self.fc  = nn.Linear(hidden, vocab_size)

    def forward(self, x):
        # x: [B, T]
        e = self.emb(x)                     # [B, T, E]
        _, hT = self.gru(e)                # hT: [layers, B, H]
        h = hT[-1]                         # [B, H] last layer
        logits = self.fc(h)                # [B, V]
        return logits

model = CharGRU(vocab_size=vocab_size, emb_dim=64, hidden=128, num_layers=1).to(device)
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.AdamW(model.parameters(), lr=3e-3)
sum(p.numel() for p in model.parameters())/1e6

#%%

EPOCHS = 5  # keep small for <~1.5 min
best_val = float('inf')
history = {"train_loss": [], "val_loss": []}

for epoch in range(1, EPOCHS+1):
    model.train()
    tl = 0.0; n=0
    for xb, yb in train_loader:
        xb, yb = xb.to(device), yb.to(device)
        optimizer.zero_grad()
        logits = model(xb)
        loss = criterion(logits, yb)
        loss.backward()
        optimizer.step()
        tl += loss.item()*xb.size(0); n += xb.size(0)
    train_loss = tl/n

    model.eval()
    vl = 0.0; m=0
    with torch.no_grad():
        for xb, yb in val_loader:
            xb, yb = xb.to(device), yb.to(device)
            logits = model(xb)
            loss = criterion(logits, yb)
            vl += loss.item()*xb.size(0); m += xb.size(0)
    val_loss = vl/m

    history["train_loss"].append(train_loss)
    history["val_loss"].append(val_loss)
    print(f"Epoch {epoch:02d} | train_loss={train_loss:.4f} | val_loss={val_loss:.4f}")

#%%

@torch.no_grad()
def predict_next(prefix_ids):
    # prefix_ids: list[int] including SOS and current chars (no EOS yet)
    x = torch.tensor([prefix_ids], dtype=torch.long, device=device)
    logits = model(x)              # [1, V]
    probs = torch.softmax(logits, dim=-1).squeeze(0)  # [V]
    next_id = int(torch.argmax(probs).item())
    return next_id

@torch.no_grad()
def autocomplete(prefix: str, max_new=20):
    # Build initial prefix with SOS + typed chars
    cur = [stoi["<s>"]] + [stoi[c] for c in prefix]
    for _ in range(max_new):
        nxt = predict_next(cur)
        if nxt == stoi["</s>"]:
            break
        cur.append(nxt)
    return decode(cur)
    
# Quick test
for p in ["a", "ab", "compu", "univer", "micro", "inter", "progr"]:
    print(p, "->", autocomplete(p))

#%%

def half_prefix(w: str):
    k = max(1, len(w)//2)  # at least one character
    return w[:k]

@torch.no_grad()
def exact_match_rate(word_list, limit=2000):
    # compute exact match on subset for speed
    total = 0
    correct = 0
    for w in word_list[:limit]:
        pred = autocomplete(half_prefix(w))
        total += 1
        if pred == w:
            correct += 1
    return correct/total if total>0 else 0.0

emr = exact_match_rate(val_words, limit=2000)
print(f"Exact‑match on half‑prefix (val subset): {emr*100:.2f}%")

#%% md

### Written Answer — Encoding letters as integers & the role of Embedding layers

**Claim:** “Converting letters into their alphabetical indices is a bad encoding for learning models.”

**Short answer:** This is true **if you feed the raw integers directly** into the model as numeric inputs. The model would then infer a fake notion of *order* and *distance* (e.g., `'b'(2)` is “twice” `'a'(1)` and “closer” to `'c'(3)`) that is **not** meaningful for categorical symbols.  
However, **if you use an embedding layer**, the integers are treated as **IDs**, not magnitudes. The embedding performs a learned lookup to a dense vector space where notions of similarity are learned from data. Thus, the “bad encoding” problem effectively disappears in practice.

**Why raw integer encoding is problematic (same reasons as for categorical data):**
- Injects **spurious order** and **Euclidean distance** between categories.
- Can bias linear models or MLPs to interpret “higher index ⇒ larger value.”
- Not robust under **relabeling** (if you permute the integer codes, predictions change).

**What is an Embedding layer? Why use it?**
- An **Embedding** is a trainable lookup table: `index → vector` (e.g., 64‑D).
- It treats indices as **keys**, not numeric values; their **vectors** are learned end‑to‑end.
- It yields **dense, low‑dimensional** representations with semantic structure (similar items get similar vectors), enabling better generalization and efficiency than one‑hot encodings.

#%%

# Try your own prefixes here:
tests = ["algo", "theo", "mathe", "phys", "graph", "learn", "deepl", "stat", "optim", "autoco", "neura"]
for t in tests:
    print(f"{t:>10s} -> {autocomplete(t)}")

#%%

import json, pickle
OUT_DIR = Path('/mnt/data/rnn_autocomplete_artifacts')
OUT_DIR.mkdir(exist_ok=True, parents=True)

# Save training losses
(OUT_DIR/'history.json').write_text(json.dumps(history, indent=2))

# Save model weights
torch.save({'model_state': model.state_dict(),
            'vocab': {'itos': itos, 'stoi': stoi}}, OUT_DIR/'char_gru.pt')

print("Artifacts saved to:", str(OUT_DIR))

#%% md

### Conclusion
We built a compact character‑level GRU that learns to autocomplete words from half‑typed prefixes.  
For classroom timing constraints, we limited the dataset size, model capacity, and epochs so it runs in ~1–1.5 minutes on CPU. You can improve accuracy by increasing the number of epochs, hidden size, or the number of usable words (at the cost of runtime).
