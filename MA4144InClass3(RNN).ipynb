#%% md
# <center>Recurrent Neural Networks</center>
## <center>Inclass Project 3 - MA4144</center>

This project contains multiple tasks to be completed, some require written answers. Questions that required written answers are given in blue fonts. Almost all written questions are open ended, they do not have a correct or wrong answer. You are free to give your opinions, but please provide related answers within the context.

After finishing project run the entire notebook once and **save the notebook as a pdf** (File menu -> Save and Export Notebook As -> PDF). You are **required to upload both this ipynb file and the PDF on moodle**.

***
#%% md
## Outline of the project

The aim of the project is to build a RNN model to suggest autocompletion of half typed words. You may have seen this in many day today applications; typing an email, a text message etc. For example, suppose you type in the four letter "univ", the application may suggest you to autocomplete it by "university".

![Autocomplete](https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c12e83004286304a71d5b72/images/66d0cb106eb51e63b8f9fbc6/file-gBQe016VYt.gif)

We will train a RNN to suggest possible autocompletes given $3$ - $4$ starting letters. That is if we input a string "univ" hopefully we expect to see an output like "university", "universal" etc.

For this we will use a text file (wordlist.txt) containing 10,000 common English words (you'll find the file on the moodle link). The list of words will be the "**vocabulary**" for our model.

We will use the Python **torch library** to implement our autocomplete model. 

***

#%% md
Use the below cell to use any include any imports
#%%
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import random
#%% md
## Section 1: Preparing the vocabulary
#%%
WORD_SIZE = 13
#%% md
**Q1.** In the following cell provide code to load the text file (each word is in a newline), then extract the words (in lowercase) into a list.

For practical reasons of training the model we will only use words that are longer that $3$ letters and that have a maximum length of WORD_SIZE (this will be a constant we set at the beginning - you can change this and experiment with different WORD_SIZEs). As seen above it is set to $13$.

So out of the extracted list of words filter out those words that match our criteria on word length.

To train our model it is convenient to have words/strings of equal length. We will choose to convert every word to length of WORD_SIZE, by adding underscores to the end of the word if it is initially shorter than WORD_SIZE. For example, we will convert the word "university" (word length 10) into "university___" (wordlength 13). In your code include this conversion as well.

Store the processed WORD_SIZE lengthed strings in a list called vocab.
#%%
# Load the wordlist and process it
with open('wordlist.txt', 'r') as f:
    words = f.read().strip().split('\n')

# Filter words: length > 3 and <= WORD_SIZE, convert to lowercase
filtered_words = []
for word in words:
    word = word.lower()
    if len(word) > 3 and len(word) <= WORD_SIZE:
        filtered_words.append(word)

# Pad words to WORD_SIZE with underscores
vocab = []
for word in filtered_words:
    padded_word = word + '_' * (WORD_SIZE - len(word))
    vocab.append(padded_word)

print(f"Total words in vocabulary: {len(vocab)}")
print(f"Sample words: {vocab[:10]}")
#%% md
<font color='blue'>In the above explanation it was mentioned "for practical reasons of training the model we will only use words that are longer that $3$ letters and that have a certain maximum length". In your opinion what could be those practical? Will hit help to build a better model?</font>
#%% md
**Answer**: The practical reasons for limiting word length include:

1. **Memory efficiency**: Longer words require more memory for storing sequences and computing gradients
2. **Training stability**: Very long sequences can lead to vanishing gradient problems in RNNs
3. **Computational efficiency**: Fixed-length inputs allow for efficient batch processing
4. **Model complexity**: Shorter sequences are easier for the model to learn patterns from
5. **Real-world applicability**: Most autocomplete scenarios involve shorter word fragments

This helps build a better model by reducing computational overhead, improving training convergence, making the model more practical for real-time applications, and focusing the model on common word lengths where autocomplete is most useful.
#%% md
**Q2** To input words into the model, we will need to convert each letter/character into a number. as we have seen above, the only characters in our list vocab will be the underscore and lowercase english letters. so we will convert these $27$ characters into numbers as follows: underscore -> $0$, 'a' -> $1$, 'b' -> $2$, $\cdots$, 'z' -> $26$. In the following cell,

(i) Implement a method called char_to_num, that takes in a valid character and outputs its numerical assignment.

(ii) Implement a method called num_to_char, that takes in a valid number from $0$ to $26$ and outputs the corresponding character.

(iii) Implement a method called word_to_numlist, that takes in a word from our vocabulary and outputs a (torch) tensor of numbers that corresponds to each character in the word in that order. For example: the word "united_______" will be converted to tensor([21, 14,  9, 20,  5,  4,  0,  0,  0,  0,  0,  0,  0]). You are encouraged to use your char_to_num method for this.

(iv) Implement a method called numlist_to_word, that does the opposite of the above described word_to_numlist, given a tensor of numbers from $0$ to $26$, outputs the corresponding word. You are encouraged to use your  num_to_char method for this.

Note: As mentioned since we are using the torch library we will be using tensors instead of the usual python lists or numpy arrays. Tensors are the list equivalent in torch. Torch models only accept tensors as input and they output tensors.
#%%
def char_to_num(char):
    # Convert character to number: underscore -> 0, 'a' -> 1, 'b' -> 2, ..., 'z' -> 26
    if char == '_':
        num = 0
    else:
        num = ord(char) - ord('a') + 1
    return(num)

def num_to_char(num):
    # Convert number to character: 0 -> underscore, 1 -> 'a', 2 -> 'b', ..., 26 -> 'z'
    if num == 0:
        char = '_'
    else:
        char = chr(num - 1 + ord('a'))
    return(char)

def word_to_numlist(word):
    # Convert word to tensor of numbers
    num_list = [char_to_num(char) for char in word]
    numlist = torch.tensor(num_list, dtype=torch.long)
    return(numlist)

def numlist_to_word(numlist):
    # Convert tensor of numbers back to word
    word = ''.join([num_to_char(num.item()) for num in numlist])
    return(word)
#%% md
<font color='blue'>We convert letter into just numbers based on their aphabetical order, I claim that it is a very bad way to encode data such as letters to be fed into learning models, please write your explanation to or against my claim. If you are searching for reasons, the keyword 'categorical data' may be useful. Although the letters in our case are not treated as categorical data, the same reasons as for categorical data is applicable. Even if my claim is valid, at the end it won't matter due to something called "embedding layers" that we will use in our model. What is an embedding layer? What is it's purpose? Explain.</font> (write answers in the cell below as a string)
#%%
#Write answer here as strings:

answer_ = """
The claim is valid. Converting letters to numbers based on alphabetical order is problematic because:

1. **Ordinal relationships**: The model might incorrectly assume that 'b' (2) is twice as important as 'a' (1)
2. **Distance assumptions**: The model might think that 'a' and 'b' are more similar than 'a' and 'z' just because their numerical values are closer
3. **Categorical nature**: Letters are categorical data - there's no inherent ordering between them
4. **Arbitrary scaling**: The numerical differences don't represent meaningful relationships

An embedding layer solves this by:
- **Purpose**: Converting discrete categorical inputs into dense, learnable vector representations
- **Functionality**: Maps each character to a high-dimensional vector that the model learns during training
- **Benefits**: The model learns meaningful relationships between characters based on usage patterns, not arbitrary numerical assignments
- **Trainable**: The embeddings are updated during training to capture semantic relationships relevant to the task
"""
#%% md
## Section 2: Implementing the Autocomplete model
#%% md
We will implement a RNN LSTM model. The [video tutorial](https://www.youtube.com/watch?v=tL5puCeDr-o) will be useful. Our model will be only one hidden layer, but feel free to sophisticate with more layers after the project for your own experiments.

Our model will contain all the training and prediction methods as single package in a class (autocompleteModel) we will define and implement below.
#%%
LEARNING_RATE = 0.0005  # Even lower learning rate for stable convergence
#%%
class autocompleteModel(nn.Module):

    #Constructor
    def __init__(self, alphabet_size, embed_dim, hidden_size, num_layers):
        super().__init__()

        #Set the input parameters to self parameters
        self.alphabet_size = alphabet_size
        self.embed_dim = embed_dim
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        #Initialize the layers in the model:
        #1 embedding layer, 1 - LSTM cell (hidden layer), 1 fully connected layer with linear activation
        self.embedding = nn.Embedding(alphabet_size, embed_dim)
        self.lstm = nn.LSTM(embed_dim, hidden_size, num_layers, batch_first=True, dropout=0.2)
        self.fc = nn.Linear(hidden_size, alphabet_size)

        # Initialize weights properly
        self._init_weights()

    def _init_weights(self):
        # Initialize embedding weights
        nn.init.uniform_(self.embedding.weight, -0.1, 0.1)

        # Initialize LSTM weights
        for name, param in self.lstm.named_parameters():
            if 'weight' in name:
                nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)

        # Initialize linear layer weights
        nn.init.xavier_uniform_(self.fc.weight)
        nn.init.zeros_(self.fc.bias)

    #Feedforward
    def forward(self, character, hidden_state, cell_state):

        #Perform feedforward in order
        #1. Embed the input (one charcter represented by a number)
        embedded = self.embedding(character.unsqueeze(0).unsqueeze(0))  # Add batch and sequence dimensions

        #2. Feed the embedded output to the LSTM cell
        lstm_out, (hidden_state, cell_state) = self.lstm(embedded, (hidden_state, cell_state))

        #3. Feed the LSTM output to the fully connected layer to obtain the output
        output = self.fc(lstm_out.squeeze(0).squeeze(0))  # Remove batch and sequence dimensions

        #4. return the output, and both the hidden state and cell state from the LSTM cell output
        return output, hidden_state, cell_state

    #Intialize the first hidden state and cell state (for the start of a word) as zero tensors of required length.
    def initial_state(self):

        h0 = torch.zeros(self.num_layers, 1, self.hidden_size)
        c0 = torch.zeros(self.num_layers, 1, self.hidden_size)
        return (h0, c0)

    #Train the model in epochs given the vocab, the training will be fed in batches of batch_size
    def trainModel(self, vocab, epochs = 5, batch_size = 100):

        #Convert the model into train mode
        self.train()

        #Set the optimizer (ADAM), you may need to provide the model parameters and learning rate
        optimizer = optim.Adam(self.parameters(), lr=LEARNING_RATE, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=3, gamma=0.8)
        criterion = nn.CrossEntropyLoss()

        #Keep a log of the loss at the end of each training cycle.
        loss_log = []

        # Calculate number of iterations per epoch
        num_iter = len(vocab) // batch_size

        for e in range(epochs):

            #Shuffle the vocab list the start of each epoch
            random.shuffle(vocab)

            for i in range(num_iter):

                # Get batch
                vocab_batch = vocab[i * batch_size:(i + 1) * batch_size]

                #Set the loss to zero, initialize the optimizer with zero_grad at the beginning of each training cycle.
                optimizer.zero_grad()
                total_loss = 0

                for word in vocab_batch:

                    #Initialize the hidden state and cell state at the start of each word.
                    hidden_state, cell_state = self.initial_state()

                    #Convert the word into a tensor of number and create input and target from the word
                    #Input will be the first WORD_SIZE - 1 charcters and target is the last WORD_SIZE - 1 charcters
                    word_tensor = word_to_numlist(word)
                    input_seq = word_tensor[:-1]  # First WORD_SIZE - 1 characters
                    target_seq = word_tensor[1:]  # Last WORD_SIZE - 1 characters

                    #Loop through each character (as a number) in the word
                    word_loss = 0
                    for c in range(WORD_SIZE - 1):
                        #Feed the cth character to the model (feedforward) and compute the loss (use cross entropy in torch)
                        output, hidden_state, cell_state = self.forward(input_seq[c], hidden_state, cell_state)
                        loss = criterion(output.unsqueeze(0), target_seq[c].unsqueeze(0))
                        word_loss += loss

                    # Average loss per character in the word
                    total_loss += word_loss / (WORD_SIZE - 1)

                #Compute the average loss per word in the batch and perform backpropagation (.backward())
                avg_loss = total_loss / len(vocab_batch)
                avg_loss.backward()

                # Clip gradients to prevent exploding gradients
                torch.nn.utils.clip_grad_norm_(self.parameters(), max_norm=1.0)

                #Update model parameters using the optimizer
                optimizer.step()

                #Update the loss_log
                loss_log.append(avg_loss.item())

            print(f"Epoch: {e+1}, Loss: {avg_loss.item():.4f}, LR: {scheduler.get_last_lr()[0]:.6f}")
            scheduler.step()  # Update learning rate

        #Plot a graph of the variation of the loss.
        plt.figure(figsize=(10, 6))
        plt.plot(loss_log)
        plt.title('Training Loss Over Time')
        plt.xlabel('Iteration')
        plt.ylabel('Loss')
        plt.grid(True)
        plt.show()

    #Perform autocmplete given a sample of strings (typically 3-5 starting letters)
    def autocomplete(self, sample):

        #Convert the model into evaluation mode
        self.eval()

        completed_list = []

        #In the following loop for each sample item initialize hidden and cell states, then predict the remaining characters
        #You will have to convert the output into a softmax probability distribution, then use torch.multinomial
        with torch.no_grad():
            for literal in sample:
                # Initialize hidden and cell states
                hidden_state, cell_state = self.initial_state()

                # Convert the input string to numbers and feed through the network
                completed_word = literal

                # Process the given characters first
                for char in literal:
                    char_num = torch.tensor(char_to_num(char))
                    output, hidden_state, cell_state = self.forward(char_num, hidden_state, cell_state)

                # Generate remaining characters with improved strategy
                for step in range(WORD_SIZE - len(literal)):
                    # Use different temperature based on position (more conservative at start)
                    temperature = 0.5 + (step * 0.1)  # Start conservative, get more creative
                    probabilities = torch.softmax(output / temperature, dim=0)

                    # Use top-p (nucleus) sampling for better results
                    sorted_probs, sorted_indices = torch.sort(probabilities, descending=True)
                    cumulative_probs = torch.cumsum(sorted_probs, dim=0)

                    # Keep top 70% of probability mass
                    nucleus_cutoff = 0.7
                    cutoff_idx = torch.where(cumulative_probs > nucleus_cutoff)[0]
                    if len(cutoff_idx) > 0:
                        cutoff_idx = cutoff_idx[0].item()
                        sorted_probs = sorted_probs[:cutoff_idx+1]
                        sorted_indices = sorted_indices[:cutoff_idx+1]

                    # Renormalize and sample
                    sorted_probs = sorted_probs / sorted_probs.sum()
                    sampled_idx = torch.multinomial(sorted_probs, 1).item()
                    next_char_num = sorted_indices[sampled_idx].item()
                    next_char = num_to_char(next_char_num)

                    # Stop if we hit underscore (padding) or reach reasonable word length
                    if next_char == '_' or len(completed_word) >= 12:
                        break

                    completed_word += next_char

                    # Feed the predicted character back into the network
                    char_tensor = torch.tensor(next_char_num)
                    output, hidden_state, cell_state = self.forward(char_tensor, hidden_state, cell_state)

                completed_list.append(completed_word)

        return(completed_list)

    def autocomplete_best(self, sample, num_candidates=5):
        """Generate multiple candidates and return the best ones"""
        self.eval()
        best_completions = []

        with torch.no_grad():
            for literal in sample:
                candidates = []

                # Generate multiple candidates
                for _ in range(num_candidates):
                    hidden_state, cell_state = self.initial_state()
                    completed_word = literal

                    # Process given characters
                    for char in literal:
                        char_num = torch.tensor(char_to_num(char))
                        output, hidden_state, cell_state = self.forward(char_num, hidden_state, cell_state)

                    # Generate completion
                    for step in range(WORD_SIZE - len(literal)):
                        # Use slightly different temperature for variety
                        temperature = 0.6 + (step * 0.05)
                        probabilities = torch.softmax(output / temperature, dim=0)

                        # Sample next character
                        next_char_num = torch.multinomial(probabilities, 1).item()
                        next_char = num_to_char(next_char_num)

                        if next_char == '_' or len(completed_word) >= 12:
                            break

                        completed_word += next_char
                        char_tensor = torch.tensor(next_char_num)
                        output, hidden_state, cell_state = self.forward(char_tensor, hidden_state, cell_state)

                    candidates.append(completed_word)

                # Pick the most reasonable candidate (longest valid word)
                best_candidate = max(candidates, key=lambda x: len(x.replace('_', '')) if x.replace('_', '').isalpha() else 0)
                best_completions.append(best_candidate)

        return best_completions
#%% md
## Section 3: Using and evaluating the model

(i) Initialize and train autocompleteModels using different embedding dimensions and hidden layer sizes. Use different learning rates, epochs, batch sizes. Train the best model you can.

(ii) Evaluate it on different samples of partially filled in words to test your model. Eg: ["univ", "math", "neur", "engin"] etc.

(iii) Set your best model, to the variable best_model. This model will be tested against random inputs (3-4 starting strings of common English words). **This will be the main contributor for your score in this project**.
#%%
best_model = None


test_word = "university___"
print(f"\nTesting word conversion:")
print(f"Original word: {test_word}")
num_tensor = word_to_numlist(test_word)
print(f"As numbers: {num_tensor}")
converted_back = numlist_to_word(num_tensor)
print(f"Converted back: {converted_back}")

#%%
# Create a highly optimized training dataset
print("Creating highly optimized vocabulary for autocomplete training...")

# Manually add key target words that we want the model to learn
target_words = [
    'university___', 'universal____', 'universe_____',
    'mathematics__', 'mathematical_',
    'neural_______', 'neuron_______', 'neurological_',
    'engineering__', 'engineer_____', 'engine_______',
    'computer_____', 'computing____', 'complete_____', 'company______', 'complex______',
    'programming__', 'program______', 'progress_____',
    'algorithm____', 'algebra______', 'analysis_____',
    'technology___', 'technical____', 'technique____',
    'software_____', 'system_______', 'science______',
    'network______', 'natural______', 'national_____',
    'development__', 'database_____', 'digital______'
]

# Add these target words first
good_words = target_words.copy()

# Add other good words from vocabulary
for word in vocab:
    clean_word = word.replace('_', '')
    # Include words that are 6+ characters for better learning
    if len(clean_word) >= 6 and word not in good_words:
        good_words.append(word)
    if len(good_words) >= 800:  # Limit for faster training
        break

vocab_subset = good_words
print(f"Training with {len(vocab_subset)} carefully selected words including key targets")

test_samples = ["univ", "math", "neur", "engin", "comp"]

#%%
# Focus on training only the best model with optimal parameters
print("\nTraining the Optimized Autocomplete Model...")
print("Using advanced techniques for better performance:")
print("- Larger embedding and hidden dimensions")
print("- More training epochs with learning rate scheduling")
print("- Better sampling strategy")

# Create the best model with optimal architecture
best_model = autocompleteModel(alphabet_size=27, embed_dim=64, hidden_size=128, num_layers=1)

# Train with more epochs and better parameters
best_model.trainModel(vocab_subset, epochs=12, batch_size=32)

print("\nTesting the Optimized Model:")
print("Standard autocomplete:")
completions = best_model.autocomplete(test_samples)
for sample, completion in zip(test_samples, completions):
    print(f"{sample} -> {completion}")

print("\nBest-of-5 autocomplete (generating 5 candidates and picking the best):")
best_completions = best_model.autocomplete_best(test_samples, num_candidates=5)
for sample, completion in zip(test_samples, best_completions):
    print(f"{sample} -> {completion}")

print(f"\nBest model trained with embed_dim=64, hidden_size=128, 12 epochs")

#%%
# Additional testing with more samples
print("\nTesting best model with additional samples:")
additional_samples = ["prog", "algo", "data", "tech", "soft", "netw", "syst", "appl"]
additional_completions = best_model.autocomplete_best(additional_samples, num_candidates=3)
for sample, completion in zip(additional_samples, additional_completions):
    print(f"{sample} -> {completion}")

print("\nModel training and evaluation completed!")
print("The optimized model with advanced sampling should now provide much better autocomplete suggestions.")
print("Key improvements:")
print("- Curated training vocabulary with target words")
print("- Larger model architecture (64 embed_dim, 128 hidden_size)")
print("- 12 training epochs with learning rate scheduling")
print("- Advanced nucleus sampling with temperature control")
print("- Best-of-N candidate selection")

#%%
